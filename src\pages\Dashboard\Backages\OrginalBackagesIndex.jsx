import React, { useState } from 'react';
import { useQuery } from 'react-query';
import { Tooltip } from 'primereact/tooltip';
import { InputSwitch } from 'primereact/inputswitch';
import { Button } from 'primereact/button';
import { TfiTrash } from 'react-icons/tfi';
import { FiEdit } from 'react-icons/fi';
import Container from '@components/Container';
import axiosInstance from "../../../config/Axios";
import { loadStripe } from '@stripe/stripe-js';
import CreateBackageForm from '../../../pages/Dashboard/Backages/CreateBackageForm';
import ErrorUpgradePackage from '../../../pages/Dashboard/Backages/ErrorUpgradePackage';
import { motion } from 'framer-motion';

const stripePromise = loadStripe('pk_test_51Kg7JrJPZyIMVMipnIL0gpi2E3jvHhQ4h6UDReB84sBKDnuC5dATko0CkagEPc639o7dbfiY9Ub7zmG1g3M9eq0p009uekzZe3');

const fetchPackages = async () => {
  try {
    const token = localStorage.getItem('token');
    const userId = localStorage.getItem('user_id');

    if (!token || !userId) {
      console.error("Token or User ID not found in localStorage");
      return;
    }

    const response = await axiosInstance.get('packages/show-all-packages', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      params: {
        user_id: userId
      }
    });

    console.log('Response data:', response.data);
    return response.data;

  } catch (error) {
    console.error("Error fetching packages:", error);
    if (error.response) {
      console.error('Response error:', error.response.data);
    } else {
      console.error('Error message:', error.message);
    }
  }
};

function calculateRemainingTime(endDate) {
  const end = new Date(endDate);
  const now = new Date();
  return (end - now) / 1000;
}

function formatTimeRemaining(endDate) {
  const end = new Date(endDate);
  const now = new Date();
  const diff = end - now;

  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

  return `${days}d ${hours}h ${minutes}m`;
}

const PackagesDataTable = () => {
  const { data: packages = [], isLoading, isError, error } = useQuery('packages', fetchPackages);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isModalOpen2, setIsModalOpen2] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [hoveredCard, setHoveredCard] = useState(null);
  const [timeLeft, setTimeLeft] = useState("");
  const [selectedPlans, setSelectedPlans] = useState({});
  const [selectedPlan, setSelectedPlan] = useState("yearly")      //Not sure if I should integrate both into one variabe since there is a plan to add a page wide toggle button but I think I should
  const [editPackage, setEditPackage] = useState(null);               //Definitly not doing that today though, this code is monolithic and will require a decent amount of time to decypher XD
                                                                  // ¯\_(ツ)_/¯  If it works then it works



  const handleDelete = async (id) => {
    try {
      const confirmDelete = window.confirm("Are you sure you want to delete this package?");
      if (!confirmDelete) return;

      await axiosInstance.delete(`/packages/${id}`);
      alert('Package deleted successfully');
    } catch (error) {
      console.error('Error deleting package:', error);
      alert('Error deleting package');
    }
  };

  if (isLoading) return <p className="text-center">Loading...</p>;
  if (isError) return <p className="text-center text-red-500">Error: {error.message}</p>;



  function switchSubType() {
    setSelectedPlan(prev => prev === 'monthly' ? 'yearly' : 'monthly');
    setSelectedPlans({});
    console.log(selectedPlans);

  }


  const handleBuy = async (id) => {
    console.log(selectedPlans[id]);
    try {
      const newPackage = packages.find(pkg => pkg.id === id);
      if (!newPackage) {
        console.error('Package not found');
        return;
      }

      const purchasedPackage = packages.find(pkg => pkg.is_purchased && pkg.status === 'active');
      console.log('Purchased Package:', purchasedPackage);
      console.log('New Package:', newPackage);

      if (purchasedPackage) {
        const Purchased_ids = purchasedPackage.card_types.map(cardType => cardType.id);
        console.log('Purchased Card Type IDs:', Purchased_ids);

        const new_ids = newPackage.card_types.map(cardType => cardType.id);
        console.log('new Card Type IDs:', new_ids);

        const noCommonTypes = new_ids.every(id => !Purchased_ids.includes(id));
        if (noCommonTypes) {
          setErrorMessage("You cannot upgrade because the card type for this package is different from the current type.");
          setIsModalOpen2(true);
          return;
        }
      }

      // Determine subscription type
      const subType = selectedPlans[id] ?? selectedPlan;

      let normalizedSubType;
      if (typeof subType === 'boolean') {
        normalizedSubType = subType ? 'yearly' : 'monthly';
      } else {
        normalizedSubType = subType;
      }

      const selectedPrice = normalizedSubType === 'monthly'
        ? newPackage.monthly_price
        : newPackage.yearly_price;
      const duration = normalizedSubType === 'monthly' ? 1 : 12;

      localStorage.setItem('subscription_duration', duration);

      const response = await axiosInstance.post(`/packages/${id}/purchase`, {
        selected_price: selectedPrice,
        subscription_duration: duration,
      });

      console.log({ selectedPrice, subscription_duration: duration });
      const { sessionId, packageId } = response.data;

      if (sessionId) {
        const stripe = await stripePromise;
        const { error } = await stripe.redirectToCheckout({ sessionId });

        if (error) {
          console.error('Error during checkout redirect:', error.message);
          return;
        }

        const successResponse = await axiosInstance.get(`/packages/payment/success`, {
          params: {
            package_id: packageId,
            subscription_duration: localStorage.getItem('subscription_duration')
          }
        });

        if (successResponse.status === 200) {
          console.log(successResponse.data.message);
          localStorage.setItem('purchased_package_id', packageId);
        } else {
          console.error("Error: Payment verification failed.");
        }
      } else {
        console.error("Error: No session ID received");
      }
    } catch (error) {
      console.error('Error during purchase process:', error.response?.data || error);
    }
  };



  const userId = localStorage.getItem("user_id");
  const purchasedPackage = packages.find(pkg => pkg.is_purchased && pkg.purchased_by_manager_id == userId && pkg.status === 'active');
  const availablePackages = packages.filter(pkg => !pkg.is_purchased);

  // This function is replaced by handleBuy
  // const handlePurchase = async (pkg) => {
  //   const selectedPrice = selectedPlan === 'monthly' ? pkg.monthly_price : pkg.yearly_price;
  //
  //   try {
  //     const response = await axiosInstance.post(`/packages/${pkg.id}/purchase`, {
  //       selected_price: selectedPrice,
  //     });
  //
  //     const { sessionId } = response.data;
  //     const stripe = await stripePromise;
  //     stripe.redirectToCheckout({ sessionId });
  //   } catch (error) {
  //     console.error('Purchase error:', error);
  //     alert('Something went wrong. Try again.');
  //   }
  // };



  const getPlanStatus = (expiryDate) => {
    if (!expiryDate) return {
      color: 'gray',
      message: 'No active subscription',
      icon: '❓',
      remainingDays: 0,
    };

    const now = new Date();
    const expiry = new Date(expiryDate);
    const remainingDuration = expiry - now;
    const remainingDays = Math.ceil(remainingDuration / (1000 * 60 * 60 * 24));

    if (remainingDuration <= 0) {
      return {
        color: 'red',
        message: 'Subscription Expired ❌',
        icon: '❌',
        remainingDays: 0,
      };
    }

    const totalDuration = expiry - new Date();
    const remainingPercentage = (remainingDuration / totalDuration) * 100;
    let statusMessage = '';

    if (remainingPercentage > 50) {
      statusMessage = 'Subscription Active - Safe Period ✅';
    } else if (remainingPercentage >= 20 && remainingPercentage <= 50) {
      statusMessage = 'Subscription is nearing its end ⚠️';
    } else {
      statusMessage = 'Subscription is about to expire ❌';
    }

    return {
      color: remainingPercentage > 50 ? 'green' : remainingPercentage >= 20 ? 'yellow' : 'red',
      message: `${statusMessage} (${remainingDays} days remaining)`,
      icon: remainingPercentage > 50 ? '✅' : remainingPercentage >= 20 ? '⚠️' : '❌',
      remainingDays,
    };
  };

  const expiryDate = purchasedPackage?.expiry_date;
  let planStatus = getPlanStatus(expiryDate);

  const actionsBodyTemplate = (rowData, sub_type) => {
    const userType = localStorage.getItem('user_type');
    return (
      <div className="flex justify-around space-x-3">
        {userType === 'admin' && (
          <>
            <Tooltip target=".edit-icon" content="Edit" position="top" />
            <button
              className="edit-icon"
              onClick={() => {
                setEditPackage(rowData);
                setIsModalOpen(true);
              }}
            >
              <FiEdit className="text-yellow-500 hover:text-yellow-700 transition duration-200" size={20} />
            </button>

            <Tooltip target=".delete-icon" content="Delete" position="top" />
            <button
              className="delete-icon text-red-500 hover:text-red-700 transition duration-200"
              onClick={() => handleDelete(rowData.id)}
            >
              <TfiTrash className="text-red-500" size={20} />
            </button>
          </>
        )}

        {userType === 'manager' && (
          <Button
          className={`main-btn text-md shadow-md px-5 py-2 rounded-lg transition duration-200 text-white border-2 ${
            rowData.name.toLowerCase() === purchasedPackage?.name?.toLowerCase()
              ? "cursor-not-allowed"
              : ""
          }`}
          style={{
            backgroundColor: purchasedPackage
              ? rowData.name.toLowerCase() === purchasedPackage.name.toLowerCase()
                ? "#6b7280" // gray-500
                : parseInt(rowData.monthly_price) > parseInt(purchasedPackage.monthly_price)
                  ? "#00c3ac" // orange-500
                  : "#ef4444" // red-500
              : "#00c3ac", // green-600 //#00c3ac
            borderColor: purchasedPackage
              ? rowData.name.toLowerCase() === purchasedPackage.name.toLowerCase()
                ? "#6b7280"
                : parseInt(rowData.monthly_price) > parseInt(purchasedPackage.monthly_price)
                  ? "#00c3ac"
                  : "#ef4444"
              : "#00c3ac",
          }}
          onClick={() => handleBuy(rowData.id, sub_type)}
          disabled={rowData.name.toLowerCase() === purchasedPackage?.name?.toLowerCase()}
        >
          {purchasedPackage
            ? rowData.name.toLowerCase() === purchasedPackage.name.toLowerCase()
              ? "Purchased"
              : parseInt(rowData.monthly_price) > parseInt(purchasedPackage.monthly_price)
                ? "Upgrade"
                : "Downgrade"
            : "Buy"}
        </Button>
        )}
      </div>
    );
  };

  return (
    <Container className="min-h-screen">
      <div className="relative isolate bg-white px-6 py-12 sm:py-16 lg:px-8">
        {/* Background gradient effect */}
        <div className="absolute inset-x-0 -top-3 mb-9 -z-10 transform-gpu overflow-hidden px-36 blur-3xl" aria-hidden="true">
          <motion.div
            className="mx-auto aspect-[1155/678] w-[72.1875rem] opacity-80"
            style={{
              clipPath: 'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)'
            }}
            animate={{
              background: [
                'linear-gradient(45deg, #ff2299 0%, #9922ff 100%)',
                'linear-gradient(45deg, #9922ff 0%, #22aaff 100%)',
                'linear-gradient(45deg, #22aaff 0%, #ff2299 100%)'
              ],
              rotate: -360,
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear",
            }}
          />
        </div>

  {/* Improved Hourglass countdown timer - 3D design */}
{/* Improved Hourglass countdown timer - 3D design */}
{purchasedPackage && (
  <div className="pkg-countdown absolute right-8 top-1/3 -translate-y-1/2 z-10">
    <div className="glass-container mt-96 flex flex-col items-center">
      {/* Hourglass structure - improved shape */}
      <div className="glass-frame relative w-28 h-64 flex flex-col items-center justify-between">
        {/* Top wooden frame */}
        <div className="glass-top-frame absolute top-0 w-28 h-4 bg-gradient-to-r from-purple-800 via-purple-700 to-purple-800 rounded-t-lg z-20 shadow-md"></div>

        {/* Glass container with better shape */}
        <div className="glass-body relative w-full h-full flex flex-col items-center justify-between" style={{ perspective: '1000px' }}>
          {/* Top glass bulb - improved shape */}
          <div className="glass-top-bulb relative w-24 h-28 overflow-hidden z-10"
               style={{
                 clipPath: 'polygon(0% 0%, 100% 0%, 85% 85%, 50% 100%, 15% 85%)',
                 background: 'linear-gradient(to right bottom, rgba(255,255,255,0.7), rgba(193,190,255,0.5))',
                 borderRadius: '50% 50% 0 0',
                 boxShadow: 'inset 0 10px 30px -10px rgba(153,34,255,0.2), inset 0 -5px 15px -5px rgba(153,34,255,0.3)',
                 marginBottom: '-17px'
               }}>
            {/* Sand in top chamber */}
            <motion.div
              className="glass-sand-top absolute w-full bg-gradient-to-b from-pink-200 via-pink-300 to-purple-400"
              style={{
                bottom: 0,
                borderRadius: '50% 50% 0 0',
                boxShadow: 'inset 0 5px 15px -5px rgba(153,34,255,0.5)'
              }}
              initial={{ height: '100%' }}
              animate={{ height: '0%' }}
              transition={{
                duration: calculateRemainingTime(purchasedPackage.expiry_date),
                ease: "linear"
              }}
            />

            {/* Falling sand streams */}
            <div className="absolute w-full h-full overflow-hidden" style={{ zIndex: 5 }}>
              {[...Array(8)].map((_, index) => (
                <motion.div
                  key={`top-sand-stream-${index}`}
                  className="absolute bg-gradient-to-b from-pink-300 to-purple-400 rounded-full"
                  style={{
                    width: `${Math.random() * 1.5 + 0.5}px`,
                    height: `${Math.random() * 4 + 3}px`,
                    left: `${30 + Math.random() * 40}%`,
                    top: '20%',
                    filter: 'blur(0.3px)',
                    boxShadow: '0 0 3px rgba(255,105,180,0.7)'
                  }}
                  initial={{ y: -10, opacity: 0 }}
                  animate={{
                    y: 40,
                    opacity: [0, 0.9, 0]
                  }}
                  transition={{
                    duration: 0.6 + Math.random() * 0.4,
                    repeat: Infinity,
                    delay: index * 0.1,
                    ease: "easeIn"
                  }}
                />
              ))}
            </div>

            {/* Falling sand particles */}
            {[...Array(8)].map((_, index) => (
              <motion.div
                key={`top-particle-${index}`}
                className="sand-particle absolute rounded-full bg-pink-300"
                style={{
                  width: `${Math.random() * 2 + 1}px`,
                  height: `${Math.random() * 2 + 1}px`,
                  left: `${45 + (Math.random() - 0.5) * 10}%`,
                  boxShadow: '0 0 2px rgba(255,34,153,0.5)',
                  opacity: 0.8 + Math.random() * 0.2
                }}
                initial={{ y: '10%' }}
                animate={{ y: '90%' }}
                transition={{
                  duration: 0.8 + Math.random() * 0.4,
                  repeat: Infinity,
                  delay: index * 0.2,
                  ease: "easeIn"
                }}
              />
            ))}
          </div>

          {/* Neck section - completely redesigned */}
          <div className="glass-neck-section relative w-full h-7 flex justify-center items-start z-13">
            {/* Outer metallic ring */}


            {/* Main neck tube with realistic glass effect */}
            <div className="glass-neck-tube relative w-4 h-8"
                 style={{
                   clipPath: 'polygon(30% 0%, 70% 0%, 100% 100%, 0% 100%)',
                   background: 'linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(200,180,255,0.6))',
                   backdropFilter: 'blur(1px)',
                   borderLeft: '1px solid rgba(255,255,255,0.5)',
                   borderRight: '1px solid rgba(255,255,255,0.5)',
                   boxShadow: `
                     inset 2px 0 3px rgba(255,255,255,0.4),
                     inset -2px 0 3px rgba(255,255,255,0.4),
                     0 0 10px rgba(153,34,255,0.3)
                   `,
                   overflow: 'hidden',
                   marginTop: '-1px'
                 }}>

              {/* Sand stream in neck */}
              <div className="absolute w-full h-full">
                {[...Array(3)].map((_, index) => (
                  <motion.div
                    key={`neck-stream-${index}`}
                    className="absolute bg-gradient-to-b from-pink-300 to-purple-400 rounded-full"
                    style={{
                      width: `${Math.random() * 1.5 + 1}px`,
                      left: `${40 + (Math.random() - 0.5) * 20}%`,
                      top: '0%',
                      filter: 'blur(0.5px)',
                      boxShadow: '0 0 4px rgba(255,105,180,0.8)'
                    }}
                    initial={{ height: '0%' }}
                    animate={{ height: '100%' }}
                    transition={{
                      duration: 0.4 + Math.random() * 0.3,
                      repeat: Infinity,
                      delay: index * 0.1,
                      ease: "linear"
                    }}
                  />
                ))}

                {/* Sand particles in neck */}
                {[...Array(5)].map((_, index) => (
                  <motion.div
                    key={`neck-particle-${index}`}
                    className="absolute rounded-full bg-pink-300"
                    style={{
                      width: `${Math.random() * 1.5 + 0.5}px`,
                      height: `${Math.random() * 1.5 + 0.5}px`,
                      left: `${40 + (Math.random() - 0.5) * 20}%`,
                      top: `${Math.random() * 100}%`,
                      boxShadow: '0 0 2px rgba(255,34,153,0.7)',
                      opacity: 0.7 + Math.random() * 0.3
                    }}
                    initial={{ y: -10 }}
                    animate={{ y: 10 }}
                    transition={{
                      duration: 0.5 + Math.random() * 0.5,
                      repeat: Infinity,
                      delay: index * 0.2,
                      ease: "linear"
                    }}
                  />
                ))}
              </div>

              {/* Glass refraction effect */}
              <div className="absolute inset-0"
                   style={{
                     background: 'linear-gradient(90deg, rgba(255,255,255,0.2) 0%, transparent 50%, rgba(255,255,255,0.2) 100%)'
                   }}></div>
            </div>

            {/* Lower metallic ring */}
            <div className="glass-neck-ring absolute w-8 h-2 bg-gradient-to-r from-purple-700 to-purple-600 rounded-full z-30"
                 style={{
                   bottom: '-1px',
                   boxShadow: '0 -1px 3px rgba(0,0,0,0.3)',
                   borderBottom: '1px solid rgba(255,255,255,0.2)'
                 }}></div>
          </div>

          {/* Bottom glass bulb - improved shape */}
          <div className="glass-bottom-bulb relative w-24 h-28 overflow-hidden z-10"
               style={{
                 clipPath: 'polygon(15% 15%, 50% 0%, 85% 15%, 100% 100%, 0% 100%)',
                 background: 'linear-gradient(to right bottom, rgba(255,255,255,0.7), rgba(193,190,255,0.5))',
                 borderRadius: '0 0 50% 50%',
                 boxShadow: 'inset 0 -10px 30px -10px rgba(153,34,255,0.2), inset 0 5px 15px -5px rgba(153,34,255,0.3)',
                 marginTop: '-4px'
               }}>
            {/* Sand in bottom chamber */}
            <motion.div
              className="glass-sand-bottom absolute w-full bg-gradient-to-b from-pink-300 via-purple-400 to-blue-500"
              style={{
                bottom: 0,
                borderRadius: '0 0 50% 50%',
                boxShadow: 'inset 0 -5px 15px -5px rgba(34,170,255,0.5)'
              }}
              initial={{ height: '0%' }}
              animate={{ height: '100%' }}
              transition={{
                duration: calculateRemainingTime(purchasedPackage.expiry_date),
                ease: "linear"
              }}
            />

            {/* Sand pile effect at bottom */}
            <div className="sand-pile absolute bottom-0 w-full h-1/2"
                 style={{
                   background: 'radial-gradient(ellipse at center bottom, rgba(153,34,255,0.8) 0%, rgba(153,34,255,0.4) 50%, transparent 80%)',
                   opacity: 0.8
                 }}>
            </div>

            {/* Rising sand particles */}
            {[...Array(12)].map((_, index) => (
              <motion.div
                key={`bottom-particle-${index}`}
                className="sand-particle absolute rounded-full"
                style={{
                  width: `${Math.random() * 3 + 1}px`,
                  height: `${Math.random() * 3 + 1}px`,
                  left: `${20 + Math.random() * 60}%`,
                  bottom: `${5 + Math.random() * 30}%`,
                  background: `rgba(153,34,255,${0.6 + Math.random() * 0.4})`,
                  opacity: 0
                }}
                initial={{ y: 5, opacity: 0 }}
                animate={{
                  y: -10 - Math.random() * 10,
                  opacity: [0, 0.8, 0]
                }}
                transition={{
                  duration: 1 + Math.random() * 1,
                  repeat: Infinity,
                  delay: index * 0.5 + Math.random() * 2,
                  ease: "easeOut"
                }}
              />
            ))}

            {/* Sand settling effect */}
            {[...Array(6)].map((_, index) => (
              <motion.div
                key={`settle-particle-${index}`}
                className="absolute rounded-full bg-pink-300"
                style={{
                  width: `${Math.random() * 4 + 2}px`,
                  height: `${Math.random() * 2 + 1}px`,
                  left: `${20 + Math.random() * 60}%`,
                  bottom: '10%',
                  opacity: 0.7,
                  filter: 'blur(0.5px)'
                }}
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 0.7 }}
                transition={{
                  duration: 0.5,
                  repeat: Infinity,
                  repeatType: 'reverse',
                  delay: index * 0.3
                }}
              />
            ))}
          </div>
        </div>

        {/* Bottom wooden frame */}
        <div className="glass-bottom-frame absolute bottom-0 w-28 h-4 bg-gradient-to-r from-purple-800 via-purple-700 to-purple-800 rounded-b-lg z-20 shadow-md"></div>

        {/* Base with shadow */}
        <div className="glass-base absolute -bottom-3 w-32 h-3 bg-gradient-to-r from-purple-900 via-blue-800 to-purple-900 rounded-lg shadow-lg z-10"></div>

        {/* 3D effect edge highlights */}
        <div className="glass-highlight-left absolute left-0 top-0 bottom-0 w-1 z-30"
             style={{
               background: 'linear-gradient(to right, rgba(255,255,255,0.4), transparent)',
               borderRadius: '50% 0 0 50%'
             }}>
        </div>
        <div className="glass-highlight-right absolute right-0 top-0 bottom-0 w-1 z-30"
             style={{
               background: 'linear-gradient(to left, rgba(255,255,255,0.3), transparent)',
               borderRadius: '0 50% 50% 0'
             }}>
        </div>

        {/* Glass reflection overlay */}
        <div className="glass-reflection-overlay absolute inset-0 z-25 pointer-events-none"
             style={{
               background: 'linear-gradient(135deg, rgba(255,255,255,0.15) 0%, transparent 40%, rgba(153,34,255,0.05) 80%, rgba(34,170,255,0.1) 100%)',
               borderRadius: '8px'
             }}>
        </div>
      </div>

      {/* Time display with improved styling */}
      <div className="glass-time-display mt-6 mb-9  bg-gradient-to-b from-purple-50 to-purple-100/90 backdrop-blur-sm p-3 rounded-lg shadow-lg border border-purple-200/50">
        <div className="text-center mb-15">
            <p className="glass-time-label text-xs font-semibold text-purple-800 mb-1">REMAINING TIME</p>
            <p className="glass-time-value text-xl font-bold text-purple-700">
              {/* Check if the expiry date is valid and in the future */}
              {purchasedPackage?.expiry_date && new Date(purchasedPackage.expiry_date) > new Date() ?
                // If the subscription is active, show the time left
                // Use timeLeft if it's available (likely for a live countdown), otherwise calculate it.
                (timeLeft || formatTimeRemaining(purchasedPackage.expiry_date)) :
                // If the subscription is expired or expiry_date is invalid/missing, show "Expired"
                "Expired"
              }
            </p>
            <p className="glass-status-message text-xs mt-1">
              <span className={
                planStatus.color === 'green' ? 'text-green-600' :
                planStatus.color === 'yellow' ? 'text-purple-600' :
                'text-pink-600'
              }>
                {planStatus.icon} {planStatus.message.split('(')[0].trim()}
              </span>
            </p>
        </div>
      </div>

      {/* Reflection effect */}
      <div className="glass-reflection w-20 h-1 mt-2 opacity-70 blur-sm"
           style={{
             background: 'radial-gradient(ellipse at center, rgba(153,34,255,0.4) 0%, transparent 70%)'
           }}>
      </div>
    </div>
  </div>
)}

{/* Add these improved styles to your CSS */}
<style dangerouslySetInnerHTML={{__html: `
  .glass-frame {
    transform-style: preserve-3d;
    transform: rotateX(5deg);
    transition: transform 0.5s ease;
  }


  .glass-body::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.15) 0%, transparent 50%, rgba(153,34,255,0.08) 100%);
    pointer-events: none;
    z-index: 30;
    border-radius: 8px;
  }

  @keyframes pulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
  }

  .sand-particle {
    filter: blur(0.5px);
    animation: pulse 3s infinite;
  }

  .glass-neck-connector {
    perspective: 500px;
  }

  .glass-neck-tube {
    transition: all 0.3s ease;
    transform-origin: top center;
  }

  .glass-frame:hover .glass-neck-tube {
    transform: scaleY(1.1);
    box-shadow: inset 0 0 10px rgba(153,34,255,0.4), 0 0 12px rgba(153,34,255,0.3);
  }

  .glass-time-display:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(153,34,255,0.3);
  }

  .glass-top-bulb, .glass-bottom-bulb {
    backdrop-filter: blur(2px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  @keyframes sand-flow {
    0% { transform: translateY(-10px); opacity: 0; }
    20% { opacity: 0.9; }
    100% { transform: translateY(15px); opacity: 0; }
  }
`}} />

      {!purchasedPackage && (
        <div className="mx-auto max-w-4xl  text-center">
          <h2 className="text-base font-semibold leading-7 text-indigo-600">Packages</h2>
          <h1 className="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
            Choose the right plan for your business
          </h1>
          <p className="mx-auto mt-6 max-w-2xl text-lg leading-8 text-gray-600">
            Select an affordable plan that fits your needs and helps you grow your business.
          </p>
        </div>
      )}


        {/* Current Plan Section */}
        {purchasedPackage && (
          <div className="mt-19 mx-auto max-w-2xl">
            <div
              className={`relative rounded-3xl p-8 ring-1 ring-gray-900/10 shadow-xl transition-all duration-300 hover:shadow-2xl ${
                planStatus.color === 'green' ? 'bg-gradient-to-br from-green-50 to-white' :
                planStatus.color === 'yellow' ? 'bg-gradient-to-br from-yellow-50 to-white' :
                'bg-gradient-to-br from-red-50 to-white'
              }`}
              style={{ borderTop: `6px solid ${planStatus.color}` }}
            >
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Your Current Plan</h3>
              <div className="flex items-baseline gap-x-2 mt-4">
                <span className="text-4xl font-bold tracking-tight text-gray-900">${purchasedPackage.total_price}</span>
                <span className="text-base font-medium text-gray-500">/month</span>
              </div>
              <p className="mt-4 text-lg font-medium text-gray-700">{purchasedPackage.name}</p>

              <div className="mt-6 space-y-4">
                <div className="flex items-center">
                  <svg className="h-5 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                  </svg>
                  <span className="ml-3 text-gray-600">Card Limit: {purchasedPackage.card_limit}</span>
                </div>
                <div className="flex items-center">
                  <svg className="h-5 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                  </svg>
                  <span className="ml-3 text-gray-600">Card Types: {purchasedPackage.card_type_names.join(', ')}</span>
                </div>
              </div>

              <div className={`mt-6 p-3 rounded-lg bg-${planStatus.color}-100 text-${planStatus.color}-800 flex items-center justify-center`}>
                <span className="font-medium">{planStatus.icon} {planStatus.message}</span>
              </div>
            </div>
          </div>
        )}






{/* Admin Create Package Button */}
        {localStorage.getItem('user_type') === 'admin' && (
          <div className="mt-12 text-center">
            <button
              onClick={() => setIsModalOpen(true)}
              className="main-btn text-md shadow-md px-5 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200"
            >
              Create New Package
            </button>
          </div>
        )}
      </div>

      <CreateBackageForm   isModalOpen={isModalOpen}
  setIsModalOpen={setIsModalOpen}
  packageToEdit={editPackage}
  onSuccess={(updatedPackage, action) => {
    if (action === 'updated') {
      //
    }
    setIsModalOpen(false);
    setEditPackage(null);
  }} />


{/* Available Packages Section */}
<div className="mt-16 mx-auto max-w-7xl">
  {availablePackages.length > 0 && (
    <>
      <div className="flex justify-between items-center mb-8">
        {/* Heading on the left */}
        <h2 className="text-2xl font-bold tracking-tight text-gray-900">
          Available Packages
        </h2>

        {/* Static Toggle Button Group on the right */}
        <div className="inline-flex bg-purple-100 p-1 rounded-lg space-x-1">
          <div className="flex flex-col items-center mr-3">
            <span
              className={`text-xl font-medium ${
                selectedPlan === 'm' || selectedPlan === 'monthly'
                  ? 'text-indigo-600'
                  : 'text-gray-400'
              }`}
            >
              Monthly
            </span>
            <span
              className={`text-xl font-medium ${
                selectedPlan === 'y' || selectedPlan === 'yearly'
                  ? 'text-indigo-600'
                  : 'text-gray-400'
              }`}
            >
              Yearly
            </span>
          </div>

          <button
            onClick={() => switchSubType()}
            className={`
              relative inline-flex h-12 w-24 items-center rounded-full
              cursor-pointer transition duration-300 ease-in-out
              focus:outline-none focus:ring-2 focus:ring-offset-0
              shadow-md bg-gray-400 hover:bg-gray-500
              ${
                selectedPlan === 'm' || selectedPlan === 'monthly'
                  ? 'focus:ring-[#bd09e6]'
                  : 'focus:ring-[#0066ff]'
              }
            `}
          >
            <span
              className={`
                inline-block h-8 w-8 transform rounded-full bg-white
                shadow-xl ring-1 ring-inset ring-gray-300
                transition-transform duration-300 ease-in-out
                ${
                  selectedPlan === 'm' || selectedPlan === 'monthly'
                    ? 'translate-x-1'
                    : 'translate-x-14'
                }
              `}
            />
          </button>
        </div>
      </div>

{/* Available Packages Section - Grid Layout */}
      <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
        {availablePackages.map((pkg, index) => {
          const currentPlan = selectedPlans[pkg.id] ?? selectedPlan;
          const isYearly = currentPlan === 'yearly';
          const price = isYearly ? pkg.yearly_price : pkg.monthly_price;

          const uniqueConnections = [...new Set(pkg.type_of_connection)];

          return (
            <div
              key={pkg.id}
              className={`relative rounded-3xl p-8 ring-1 ring-gray-900/10 shadow-lg transition-all duration-300 hover:shadow-xl hover:-translate-y-2 ${
                hoveredCard === index ? 'ring-2 ring-indigo-500' : ''
              }`}
              onMouseEnter={() => setHoveredCard(index)}
              onMouseLeave={() => setHoveredCard(null)}
            >
              <div className="flex items-baseline gap-x-2">
              <h3 className="text-2xl font-semibold leading-8 text-indigo-600">{pkg.name}</h3>
              <span className="text-base font-medium text-gray-500">
                  {purchasedPackage && pkg.name.toLowerCase() === purchasedPackage.name.toLowerCase()
                    ? '(Current Plan)'
                    : ''}
                </span>
              </div>

              <div className="mt-4 flex items-baseline gap-x-2">
                <span className="text-4xl font-bold tracking-tight text-gray-900">
                  ${price}
                </span>
                <span className="text-base font-medium text-gray-500">
                  /{isYearly ? 'year' : 'month'}
                </span>
              </div>



              <ul role="list" className="mt-20 space-y-4 text-sm leading-6 text-gray-600">
                <li className="flex gap-x-3">
                  <svg className="h-6 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                  </svg>
                  Card Limit: {pkg.card_limit}
                </li>

                {/* Card Types with Badges */}
                <li className="flex flex-col gap-2">
                  <div className="flex gap-x-3">
                    <svg className="h-6 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                    </svg>
                    <span>Card Types:</span>
                  </div>
                  <div className="grid grid-cols-2 gap-2 ml-8">
                    {pkg.card_type_names.map((type, i) => (
                      <span key={i} className="px-2 py-1 text-xs font-medium rounded-full bg-indigo-100 text-indigo-800">
                        {type}
                      </span>
                    ))}
                  </div>
                </li>

                {/* Connection Types with Badges */}
                <li className="flex flex-col gap-2">
                  <div className="flex gap-x-3">
                    <svg className="h-6 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                    </svg>
                    <span>Connection Types:</span>
                  </div>
                  <div className="flex flex-wrap gap-2 ml-8">
                    {uniqueConnections.map((conn, i) => (
                      <span key={i} className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                        {conn}
                      </span>
                    ))}
                  </div>
                </li>

                {/* Number Of Colors per Card Type */}
                <li className="flex flex-col gap-2">
                  <div className="flex gap-x-3">
                    <svg className="h-6 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                    </svg>
                    <span>Number Of Colors:</span>
                  </div>
                  <ul className="ml-8 space-y-2">
                    {pkg.card_type_names.map((type, i) => (
                      <li key={i} className="flex items-center justify-between">
                        <span className="text-gray-700">{type}</span>
                        <span className="px-2 py-1 text-xs font-bold rounded-full bg-purple-100 text-purple-800">
                          {pkg.number_of_colors[i] ?? 'N/A'} colors
                        </span>
                      </li>
                    ))}
                  </ul>
                </li>
              </ul>

              <div className="mt-4 flex items-center justify-center gap-4">
                <span style={{ color: currentPlan === 'monthly' ? '#bd09e6' : '#9ca3af' }}>
                  Monthly
                </span>
                <InputSwitch
                  checked={currentPlan === 'yearly'}
                  onChange={(e) => {
                    setSelectedPlans(prev => ({
                      ...prev,
                      [pkg.id]: e.value ? 'yearly' : 'monthly'
                    }));
                  }}
                />
                <div className="flex items-center gap-2">
                  <span className={isYearly ? 'text-indigo-600' : 'text-gray-400'}>
                    Yearly
                  </span>
                </div>
              </div>

              <div className="mt-8 flex justify-center">
                {actionsBodyTemplate(pkg, isYearly)}
              </div>
            </div>
          );
        })}
      </div>
    </>
  )}

  {/* Enterprise Solution Package - Always Visible */}
  <div className="mt-16">
    <h2 className="text-2xl font-bold tracking-tight text-gray-900 mb-8">
      Enterprise Solution
    </h2>
    <div className="grid grid-cols-1 gap-8">
      <div
        className="relative rounded-3xl p-8 ring-1 ring-indigo-500 shadow-lg transition-all duration-300 hover:shadow-xl hover:-translate-y-2 bg-gradient-to-br from-indigo-50 to-white"
        onMouseEnter={() => setHoveredCard('enterprise')}
        onMouseLeave={() => setHoveredCard(null)}
        style={{ borderTop: '6px solid #4f46e5' }}
      >
        <div className="absolute top-4 right-4">
          <span className="inline-flex items-center rounded-full bg-indigo-100 px-3 py-1 text-sm font-medium text-indigo-800">
            Custom Solution
          </span>
        </div>

        <div className="flex items-baseline gap-x-2">
          <h3 className="text-2xl font-semibold leading-8 text-indigo-600">Enterprise Package</h3>
        </div>

        <div className="mt-4 flex items-baseline gap-x-2">
          <span className="text-4xl font-bold tracking-tight text-gray-900">
            Custom
          </span>
          <span className="text-base font-medium text-gray-500">
            pricing
          </span>
        </div>

        <p className="mt-6 text-base text-gray-600">
          Need a tailored solution for your business? Our enterprise package offers customized features, dedicated support, and flexible pricing to meet your specific requirements.
        </p>
        {/*This is a list of features for the enterprise package */}
        {/* <ul role="list" className="mt-8 space-y-4 text-sm leading-6 text-gray-600">
          <li className="flex gap-x-3">
            <svg className="h-6 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
            </svg>
            Unlimited card limit
          </li>
          <li className="flex gap-x-3">
            <svg className="h-6 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
            </svg>
            All card types included
          </li>
          <li className="flex gap-x-3">
            <svg className="h-6 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
            </svg>
            All connection types
          </li>
          <li className="flex gap-x-3">
            <svg className="h-6 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
            </svg>
            Priority support
          </li>
          <li className="flex gap-x-3">
            <svg className="h-6 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
            </svg>
            Custom branding options
          </li>
          <li className="flex gap-x-3">
            <svg className="h-6 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
            </svg>
            Dedicated account manager
          </li>
        </ul> */}

        <div className="mt-8 flex flex-col items-center justify-center space-y-4">
          <div className="flex items-center space-x-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
              <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
              <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
            </svg>
            <span className="text-gray-700 font-medium"><EMAIL></span>
          </div>
          <div className="flex items-center space-x-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
              <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
            </svg>
            <span className="text-gray-700 font-medium">+1 (123) 456-7890</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

      <ErrorUpgradePackage
        isModalOpen2={isModalOpen2}
        setIsModalOpen2={setIsModalOpen2}
        message={errorMessage}
      />
    </Container>
  );
};

export default PackagesDataTable;