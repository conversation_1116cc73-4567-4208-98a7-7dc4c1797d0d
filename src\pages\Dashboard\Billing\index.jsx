import React, { useEffect, useState } from "react";
import Container from "@components/Container";
import axiosInstance from "../../../config/Axios";
import { useGlobalContext } from "@contexts/GlobalContext";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { InputText } from "primereact/inputtext";
import { Dropdown } from "primereact/dropdown";
import { FaSearch } from "react-icons/fa";

export default function BillingHistory() {
  const [purchaseHistory, setPurchaseHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    package_name: '',
    status: '',
  });

  const userId = localStorage.getItem("user_id");
  const { showToast } = useGlobalContext();

  const statusOptions = [
    { label: 'All Statuses', value: '' },
    { label: 'Active', value: 'active' },
    { label: 'Expired', value: 'expired' },
    { label: 'Suspended', value: 'suspended' },
  ];

  useEffect(() => {
    const fetchPurchaseHistory = async () => {
      try {
        setLoading(true);

        if (!userId) {
          showToast("error", "Error", "User ID not found");
          return;
        }

        const { data } = await axiosInstance.get(
          `/packages/${userId}/packages_history`,
          {
            params: {
              package_name: filters.package_name,
              status: filters.status,
            }
          }
        );

        setPurchaseHistory(data.history_packages || []);
      } catch (error) {
        console.error("Error fetching purchase history:", error);
        showToast("error", "Error", "Failed to fetch purchase history");
      } finally {
        setLoading(false);
      }
    };

    fetchPurchaseHistory();
  }, [userId, filters, showToast]);

  const statusBodyTemplate = (rowData) => {
    const statusClass = rowData.status === "active"
      ? "bg-[#22C55E]"
      : rowData.status === "suspended"
        ? "bg-[#F59E0B]"
        : "bg-[#DC2626]";

    return (
      <span
        className={`text-white rounded-[6px] font-bold text-sm py-2 px-3 capitalize ${statusClass} min-w-[100px] inline-block text-center`}
      >
        {rowData.status}
      </span>
    );
  };

  const priceBodyTemplate = (rowData) => {
    return (
      <span
        className="bg-transparent text-[#00CC32] border-2 border-[#00CC32] rounded-[6px] font-bold text-sm py-2 px-3 inline-block text-center min-w-[100px]"
      >
        ${rowData.total_price}
      </span>
    );
  };

  const onFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Container className="flex flex-col h-full">
      <div className="w-full mb-5">
        <h1 className="text-2xl font-bold">Billing & Purchase History</h1>
        {/* <p className="text-gray-600 mt-2">View your package purchase history and billing information</p> */}
      </div>

      {/* Search and Filter Section - Outside the table */}
      <div className="flex justify-between items-center w-full mb-4">
        {/* Search Input */}
        <div className="flex-grow max-w-[700px] relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaSearch className="h-4 w-4 text-gray-400" aria-hidden="true" />
          </div>
          <input
            type="text"
            placeholder="Search is disabled until further notice."   //was "Search by package name..."
            className="w-full pl-10 pr-4 py-2 border rounded-md shadow-md
                      focus:outline-none focus:ring-2 focus:ring-blue-300
                      focus:border-blue-300 transition-all duration-200"
            value={filters.package_name}
            onChange={(e) => onFilterChange('package_name', e.target.value)}
            disabled
          />
        </div>

        {/* Status Filter */}
        <div className="flex items-center ml-4">
          <span className="mr-2 text-gray-700">Status:</span>
          <Dropdown
            options={statusOptions}
            value={filters.status}
            onChange={(e) => onFilterChange('status', e.value)}
            placeholder="All Statuses"
            className="w-40 shadow-md"
          />
        </div>
      </div>

      {/* Table Section - Full height */}
      <div className="w-full flex-grow overflow-hidden"> {/* Use flex-grow instead of fixed height */}
        <DataTable
          value={purchaseHistory}
          loading={loading}
          className="table w-full border"
          paginator
          rows={10}
          rowsPerPageOptions={[5, 10, 25, 50]}
          emptyMessage="No purchase history found"
          header={null} // No header since we moved search/filter outside
          scrollable
          scrollHeight="100%" // Use a more conservative height calculation
          paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
          currentPageReportTemplate="Showing {first} to {last} of {totalRecords} records"
        >
          <Column
            field="package_name"
            header="Package Name"
            sortable
          />
          <Column
            field="total_price"
            header="Total Price"
            body={priceBodyTemplate}
            sortable
          />
          <Column
            field="purchased_at"
            header="Purchase Date"
            sortable
          />
          <Column
            field="expiry_date"
            header="Expiry Date"
            sortable
          />
          <Column
            field="card_limit"
            header="Allowed Cards Count"
            sortable
          />
          <Column
            field="status"
            header="Status"
            body={statusBodyTemplate}
            sortable
          />
        </DataTable>
      </div>
    </Container>
  );
}
