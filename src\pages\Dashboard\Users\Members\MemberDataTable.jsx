import React, { useEffect, useRef, useState, useCallback } from 'react';
import { isEmpty } from 'lodash';
import parse from 'html-react-parser';
import { Dialog } from 'primereact/dialog';
import { motion } from 'framer-motion';
import { saveAs } from 'file-saver';
import { FaFileExport, FaFileImport } from 'react-icons/fa';
import TemplateImageHeader from './TemplateFilter';
import AssignGroupDialog from '../Groups/AssignGroupDialog';
import AddMemberDialog from './AddMemberDialog';
import GroupForm from '../../Backages/CreateGroupForm';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { usersTableConfig, defaultTableConfig } from '@constants';
import { useDeleteUserMutation } from '@quires/user';
import { useDataTableContext } from '@contexts/DataTableContext';
import { useGlobalContext } from '@contexts/GlobalContext';
import { useQueryParams } from '@utils/helper';
import { DataTable } from 'primereact/datatable';
import { Tooltip } from 'primereact/tooltip';
import { Column } from 'primereact/column';
import { confirmDialog, ConfirmDialog } from 'primereact/confirmdialog';
import { Toast } from 'primereact/toast';

import ImportDialog from './ImportDialog';
import axiosInstance from "../../../../config/Axios";
import { TfiTrash } from "react-icons/tfi";
import { FiEdit } from "react-icons/fi";
import { FaRegEye } from 'react-icons/fa';
import Element from '../../DesignSpace/components/Element';

import profile_img from "@images/Profile_img.jpg"

import { FaSearch } from 'react-icons/fa';
import { HiDotsVertical } from 'react-icons/hi';

import { createPortal } from 'react-dom';

const statusStyles = {
    printed: "bg-[#22C55E] ",
    unprinted: "bg-[#64748B] ",
    onProgress: "bg-[#D97706] ",
}

function MemberDataTable() {
    const { totalRecords, lazyParams, setLazyParams, data, loading } = useDataTableContext();
    const { dialogHandler, openDialog, selectedMembers, setSelectedMembers } = useGlobalContext();
    const queryParams = useQueryParams();
    const groupID = queryParams.get("group-id");
    const designID = queryParams.get("design-id");
    const deleteRow = useDeleteUserMutation();
    const [importDialogVisible, setImportDialogVisible] = useState(false);
    const [selectedMember, setSelectedMember] = useState();
    const [actionType, setActionType] = useState("create");
    const userType = localStorage.getItem('user_type');
    const activeBtn = isEmpty(selectedMembers.data);
    const [memberCards, setMemberCards] = useState([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [isCreateGroupModalOpen, setIsCreateGroupModalOpen] = useState(false);

    // Mobile responsiveness states
    const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
    const [mobileActionMenuOpen, setMobileActionMenuOpen] = useState(null);

    const toast = useRef(null);

    // States for card view
    const [selectedCard, setSelectedCard] = useState(null);
    const [allCards, setAllCards] = useState([]);
    const [currentCardIndex, setCurrentCardIndex] = useState(0);
    const [isCardViewOpen, setIsCardViewOpen] = useState(false);
    const [isFlipped, setIsFlipped] = useState(false);

    const dataHandler = useCallback((e = {}) => {
        // Avoid creating a new filters object on every call if nothing changed
        const nameFilter = { value: searchQuery, matchMode: 'contains' };

        setLazyParams(prev => {
            // Only update if something actually changed
            const newFirst = e?.first !== undefined ? e.first : prev.first;
            const newRows = e?.rows !== undefined ? e.rows : prev.rows;
            const newPage = e?.first !== undefined ? Math.floor(e.first / (e.rows || prev.rows)) : prev.page;
            const newSortField = e?.sortField !== undefined ? e.sortField : prev.sortField;
            const newSortOrder = e?.sortOrder !== undefined ? e.sortOrder : prev.sortOrder;

            // Check if filters need to be updated
            const prevNameFilter = prev.filters?.name?.value;
            const filtersChanged = prevNameFilter !== searchQuery;

            // Only create a new object if something changed
            if (
                newFirst !== prev.first ||
                newRows !== prev.rows ||
                newPage !== prev.page ||
                newSortField !== prev.sortField ||
                newSortOrder !== prev.sortOrder ||
                filtersChanged
            ) {
                return {
                    ...prev,
                    filters: {
                        ...prev.filters,
                        name: nameFilter
                    },
                    first: newFirst,
                    rows: newRows,
                    page: newPage,
                    sortField: newSortField,
                    sortOrder: newSortOrder,
                };
            }

            // Return the same object if nothing changed
            return prev;
        });
    }, [searchQuery, setLazyParams]);

    const handleImportSuccess = () => {
        setLazyParams(prev => ({ ...prev, ...usersTableConfig }));
        toast.current.show({
            severity: 'success',
            summary: 'Success',
            detail: 'Users imported successfully',
            life: 3000
        });
    };

    const handleGroupSuccess = () => {
        setLazyParams(prev => ({ ...prev, ...usersTableConfig }));
        setIsCreateGroupModalOpen(false);
    };
    useEffect(() => {
        const fetchMemberCards = async () => {
            try {
                const token = localStorage.getItem('token');
                const userId = localStorage.getItem('user_id');

                const response = await axiosInstance.get(`packages/show-package-by-id/${userId}`, {
                    headers: {
                        Authorization: `Bearer ${token}`,
                    }
                });

                if (response.data && response.data.cards) {
                    setMemberCards(response.data.cards);
                }
            } catch (error) {
                console.error('Error fetching member cards:', error);
            }
        };

        fetchMemberCards();
    }, []);

    useEffect(() => {
        setLazyParams({
            ...defaultTableConfig,
            ...usersTableConfig,
            groupID: groupID,
            designID: designID
        })
    }, [groupID, designID, setLazyParams])

    // Use debounce for search query changes to avoid excessive API calls
    useEffect(() => {
        // Skip initial render
        const timeout = setTimeout(() => {
            // Only call dataHandler if searchQuery actually changed
            if (lazyParams.filters?.name?.value !== searchQuery) {
                dataHandler();
            }
        }, 500); // Increased debounce time to 500ms for better performance

        return () => clearTimeout(timeout);
    }, [searchQuery, dataHandler, lazyParams.filters?.name?.value]);

    // Mobile detection useEffect
    useEffect(() => {
        const handleResize = () => {
            const mobileView = window.innerWidth < 768;
            setIsMobile(mobileView);
            if (!mobileView) {
                setMobileActionMenuOpen(null); // Close mobile menu when switching to desktop
            }
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    const createMember = () => {
        setActionType("create")
        setSelectedMember({});
        dialogHandler("addMember");
    }

    const createGroup = () => {
        if (!isEmpty(selectedMembers.data)) {
            console.log("Selected members for group creation:", selectedMembers.data);
            setIsCreateGroupModalOpen(true);
        }
    }

    const editMember = (data) => {
        setActionType("update");
        const updatedData = { ...data };
        delete updatedData.role;
        delete updatedData.group_permission;
        delete updatedData.design;
        setSelectedMember(updatedData);
        dialogHandler("addMember");
    }


    const handleExport = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axiosInstance.get('users/export', {
                responseType: 'blob',
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            const blob = new Blob([response.data], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            });
            saveAs(blob, `users_export_${new Date().toISOString().split('T')[0]}.xlsx`);

            toast.success('Data exported successfully');
        } catch (error) {
            console.error('Error exporting data:', error);
            toast.error('Failed to export data');
        }
    };

    const deleteAdHandler = async (rowData) => {
        await deleteRow.mutateAsync({
            id: rowData?.id,
        }, {
            onSuccess: () => {
                setLazyParams(prev => ({ ...prev, ...usersTableConfig }));

                // Show success toast
                toast.current.show({
                    severity: 'success',
                    summary: 'Success',
                    detail: 'Member deleted successfully',
                    life: 3000
                });
            },
            onError: () => {
                // Show error toast
                toast.current.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to delete member',
                    life: 3000
                });
            }
        })
    }

    // Removed unused deleteRowHandler function

    const viewMemberCard = (rowData) => {
        if (rowData.cards && rowData.cards.length > 0) {
            setAllCards(rowData.cards);
            setCurrentCardIndex(0);
            setSelectedCard(rowData.cards[0]);
            console.log("ur crds",rowData.cards)
            setIsCardViewOpen(true);
        }
    };

    // Navigation functions for carousel
    const goToNextCard = () => {
        if (currentCardIndex < allCards.length - 1) {
            const nextIndex = currentCardIndex + 1;
            setCurrentCardIndex(nextIndex);
            setSelectedCard(allCards[nextIndex]);
            setIsFlipped(false); // Reset flip state when changing cards
        }
    };

    const goToPreviousCard = () => {
        if (currentCardIndex > 0) {
            const prevIndex = currentCardIndex - 1;
            setCurrentCardIndex(prevIndex);
            setSelectedCard(allCards[prevIndex]);
            setIsFlipped(false); // Reset flip state when changing cards
        }
    };

    const goToCard = (index) => {
        if (index >= 0 && index < allCards.length) {
            setCurrentCardIndex(index);
            setSelectedCard(allCards[index]);
            setIsFlipped(false); // Reset flip state when changing cards
        }
    };

    // Keyboard navigation
    useEffect(() => {
        const handleKeyPress = (event) => {
            if (!isCardViewOpen || allCards.length <= 1) return;

            switch (event.key) {
                case 'ArrowLeft':
                    event.preventDefault();
                    goToPreviousCard();
                    break;
                case 'ArrowRight':
                    event.preventDefault();
                    goToNextCard();
                    break;
                case 'Escape':
                    event.preventDefault();
                    setIsCardViewOpen(false);
                    setIsFlipped(false);
                    setCurrentCardIndex(0);
                    setAllCards([]);
                    break;
                default:
                    break;
            }
        };

        if (isCardViewOpen) {
            document.addEventListener('keydown', handleKeyPress);
        }

        return () => {
            document.removeEventListener('keydown', handleKeyPress);
        };
    }, [isCardViewOpen, allCards.length, currentCardIndex]);

    const handleDeleteClick = (rowData) => {
        console.log(rowData)
        confirmDialog({
            message: 'Are you sure you want to remove this member?',
            header: 'Confirmation',
            icon: 'pi pi-exclamation-triangle',
            acceptClassName: 'p-button-danger',
            acceptLabel: 'Yes',
            rejectLabel: 'No',
            accept: () => deleteAdHandler(rowData),
        });
    };


    const actionBodyTemplate = (rowData) => {
        const currentUserId = localStorage.getItem('user_id');



        return (
            <>
                <div className="d-inline-block text-nowrap">
                    {/* Eye Icon - View Card */}
                    {rowData.cards && rowData.cards.length > 0 && (
                        <>
                            <Tooltip target={`.eye-button-${rowData.id}`} showDelay={100} className="fs-8" />
                            <button
                                className={`btn btn-sm btn-icon eye-button-${rowData.id} me-5 text-blue-500 hover:text-blue-700`}
                                data-pr-position="bottom"
                                data-pr-tooltip="View Card"
                                onClick={() => viewMemberCard(rowData)}>
                                <FaRegEye size={20} />
                            </button>
                        </>
                    )}

                    {/* Edit */}
                    <Tooltip target={`.update-button-${rowData.id}`} showDelay={100} className="fs-8" />
                    <button
                        className={`btn btn-sm btn-icon update-button-${rowData.id} me-3`}
                        data-pr-position="bottom"
                        data-pr-tooltip="Update"
                        onClick={() => editMember(rowData)}>
                        <FiEdit />
                    </button>


                    {/* Delete */}
                    {rowData.id.toString() !== currentUserId && (
                        <>
                            <Tooltip target={`.delete-button-${rowData.id}`} showDelay={100} className="fs-8" />
                            <button
                                className={`btn btn-sm btn-icon delete-button-${rowData.id}`}
                                data-pr-position="bottom"
                                data-pr-tooltip="Delete"
                                onClick={() => handleDeleteClick(rowData)} >
                                <TfiTrash />
                            </button>
                        </>
                    )}
                </div>
            </>
        );
    }

    const selectAllHandler = (rowsData) => {
        setSelectedMembers((prev) => ({
            ...prev,
            data: rowsData,
            groupData: prev.groupData || {},
            action: !isEmpty(rowsData) ? "update" : "create"
        }));
    }

    const imageBodyTemplate = (rowData) => {
        if (rowData?.tempate_image_html) {
            return <div className="border border-black rounded-md ">{parse(rowData?.tempate_image_html)}</div>
        }
        return;
    }

    const profileBodyTemplate = (rowData) => {
        return rowData?.image ?
            <img loading="lazy" src={rowData?.image} width={100} alt="profile" />
            :
            "";
    }

    const statusBodyTemplate = (rowData) => {
        const status = rowData?.print_status || "unprinted";
        return (
            <span className={`text-[white] rounded-[6px] font-bold text-sm py-2 px-3 capitalize ${statusStyles?.[status]}`}>
                {status}
            </span>
        )
    }

    const render3DCard = () => {
        if (!selectedCard) return null;

        return (
            <div className={`flex flex-col items-center justify-center ${isMobile ? 'p-2' : 'p-6'}`}>
                <motion.div
                    className="relative"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5 }}
                    onClick={() => setIsFlipped(!isFlipped)}
                >
                    <motion.div
                        className="relative cursor-pointer"
                        style={{
                            transformStyle: 'preserve-3d',
                            perspective: '1000px',
                            width: 'fit-content',
                            maxWidth: isMobile ? '90vw' : 'none',
                        }}
                        animate={{
                            rotateY: isFlipped ? 180 : 0,
                        }}
                        transition={{
                            duration: 0.6,
                            ease: "easeInOut",
                        }}
                        whileHover={{ scale: isMobile ? 1.01 : 1.02 }}
                    >
                        {/* Card Front */}
                        <motion.div
                            className="flex flex-col items-center justify-center rounded-xl shadow-2xl overflow-hidden"
                            style={{
                                backfaceVisibility: 'hidden',
                                background: 'linear-gradient(145deg, #1a2a3a, #2a3a4a)',
                                border: '2px solid rgba(255, 255, 255, 0.1)',
                                width: 'fit-content',
                            }}
                        >
                            <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-white opacity-10 rounded-xl"></div>

                            <div className="absolute inset-0 rounded-xl overflow-hidden">
                                <div className="absolute -top-10 -left-10 w-32 h-32 bg-blue-500 rounded-full filter blur-3xl opacity-20"></div>
                                <div className="absolute -bottom-10 -right-10 w-32 h-32 bg-purple-500 rounded-full filter blur-3xl opacity-20"></div>
                            </div>

                            <div className={`relative ${isMobile ? 'p-2' : 'p-4'} flex flex-col items-center ${isMobile ? 'max-w-full' : 'max-w-sm'} mx-auto`}>
                                <div className="relative overflow-hidden" style={{ maxWidth: '100%' }}>
                                    {selectedCard.image_path ? (
                                        <motion.img
                                            src={selectedCard.image_path}
                                            alt="Card"
                                            className="object-contain"
                                            style={{
                                                maxWidth: isMobile ? '80vw' : '100%',
                                                maxHeight: isMobile ? '40vh' : 'auto'
                                            }}
                                            initial={{ opacity: 0 }}
                                            animate={{ opacity: 1 }}
                                            transition={{ delay: 0.3 }}
                                            whileHover={{ scale: isMobile ? 1.02 : 1.05 }}
                                        />
                                    ) : (
                                        <div className={`${isMobile ? 'w-full h-48' : 'w-64 h-64'} bg-gray-700 flex items-center justify-center`}>
                                            <span className="text-gray-300">No Image</span>
                                        </div>
                                    )}
                                    <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-white opacity-10"></div>

                                    <div className="absolute inset-0 overflow-hidden">
                                        {[...Array(10)].map((_, i) => (
                                            <motion.div
                                                key={i}
                                                className="absolute bg-white rounded-full"
                                                style={{
                                                    width: `${Math.random() * 3 + 1}px`,
                                                    height: `${Math.random() * 3 + 1}px`,
                                                    left: `${Math.random() * 100}%`,
                                                    top: `${Math.random() * 100}%`,
                                                    opacity: Math.random() * 0.3,
                                                }}
                                                animate={{
                                                    y: [0, (Math.random() - 0.5) * 20],
                                                    x: [0, (Math.random() - 0.5) * 20],
                                                }}
                                                transition={{
                                                    duration: Math.random() * 5 + 3,
                                                    repeat: Infinity,
                                                    repeatType: "reverse",
                                                }}
                                            />
                                        ))}
                                    </div>
                                </div>

                                <motion.div
                                    className="absolute inset-0 bg-white opacity-0 pointer-events-none"
                                    initial={{ opacity: 0 }}
                                    animate={{
                                        opacity: isFlipped ? 0 : [0, 0.1, 0],
                                        x: [-100, 300],
                                    }}
                                    transition={{
                                        duration: 1.5,
                                        repeat: Infinity,
                                        repeatDelay: 3,
                                    }}
                                    style={{
                                        background: 'linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%)',
                                    }}
                                />
                            </div>
                        </motion.div>

                        {/* Card Back */}
                        <motion.div
                            className="absolute top-0 left-0 flex flex-col items-center justify-center rounded-xl shadow-2xl overflow-hidden"
                            style={{
                                backfaceVisibility: 'hidden',
                                background: 'linear-gradient(145deg, #2a3a4a, #1a2a3a)',
                                border: '2px solid rgba(255, 255, 255, 0.1)',
                                transform: 'rotateY(180deg)',
                                width: '100%',
                                height: '100%',
                                minWidth: '300px',
                                minHeight: '400px',
                            }}
                        >
                            <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-white opacity-10 rounded-xl"></div>

                            <div className="absolute inset-0 rounded-xl overflow-hidden">
                                <div className="absolute -top-10 -left-10 w-32 h-32 bg-purple-500 rounded-full filter blur-3xl opacity-20"></div>
                                <div className="absolute -bottom-10 -right-10 w-32 h-32 bg-blue-500 rounded-full filter blur-3xl opacity-20"></div>
                            </div>

                            <div className={`relative w-full h-full ${isMobile ? 'p-3' : 'p-6'} flex flex-col justify-between`}>
                                <motion.div
                                    className={`w-full ${isMobile ? 'h-6' : 'h-8'} bg-gradient-to-r from-black to-gray-800 rounded-sm mb-4`}
                                    initial={{ opacity: 0, y: -20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.2 }}
                                />

                                <motion.div
                                    className={`w-full ${isMobile ? 'h-12' : 'h-16'} bg-white bg-opacity-10 rounded-sm flex items-center justify-center mb-4`}
                                    initial={{ opacity: 0, scale: 0.9 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    transition={{ delay: 0.3 }}
                                >
                                    <div className={`w-full ${isMobile ? 'h-8' : 'h-12'} bg-gray-800 rounded-sm flex items-center justify-center`}>
                                        <span className={`${isMobile ? 'text-xs' : 'text-xs'} text-gray-400`}>card informations</span>
                                    </div>
                                </motion.div>

                                <motion.div
                                    className="w-full space-y-3"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    transition={{ delay: 0.4 }}
                                >
                                    <div className="flex justify-between">
                                        <span className="text-xs text-blue-200">Member Name:</span>
                                        <span className="text-xs text-white">{selectedCard.member_name || 'N/A'}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-xs text-blue-200">Member Type:</span>
                                        <span className="text-xs text-white">{selectedCard.member_type || 'N/A'}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-xs text-blue-200">Serial No:</span>
                                        <span className="text-xs text-white">{selectedCard.number || 'N/A'}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-xs text-blue-200">Number of colors:</span>
                                        <span className="text-xs text-white">{selectedCard.card_type?.number_of_colors ?? 'N/A'}</span>
                                    </div>
                                </motion.div>

                                <motion.div
                                    className="mt-4 pt-2 border-t border-gray-700"
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.5 }}
                                >
                                    <div className="flex justify-between items-center">
                                        <div>
                                            <p className="text-xs text-blue-200 mb-1">Type Of Connection</p>
                                            <div className="h-8 w-24 bg-gray-700 rounded-sm"></div>
                                        </div>
                                        <div className="text-right">
                                            <p className="text-xs text-blue-200">Connection</p>
                                            <p className="text-xs text-white flex items-center justify-end">
                                                {selectedCard?.card_type?.type_of_connection === 'NFC' && (
                                                    <svg className="w-5 h-5 text-blue-300 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20C16.42 20 20 16.42 20 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                                                        <path d="M12 8C9.79 8 8 9.79 8 12C8 14.21 9.79 16 12 16C14.21 16 16 14.21 16 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                                                        <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                                                        <path d="M20 4L20 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                                                        <path d="M20 4L16 4" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                                                    </svg>
                                                )}
                                                {selectedCard?.card_type?.type_of_connection === 'Bluetooth' && (
                                                    <svg className="w-5 h-5 text-blue-300 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M6 8L18 16L12 22V2L18 8L6 16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                    </svg>
                                                )}
                                                {selectedCard?.card_type?.type_of_connection || 'N/A'}
                                            </p>
                                        </div>
                                    </div>
                                </motion.div>

                                <motion.div
                                    className="w-full text-center mt-2"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    transition={{ delay: 0.6 }}
                                >
                                    <p className="text-[8px] text-blue-300 opacity-70">
                                        This badge is property of INLNULL SYSTEM. If found, please return to nearest office.
                                    </p>
                                </motion.div>
                            </div>
                        </motion.div>
                    </motion.div>
                </motion.div>

                <div className={`${isMobile ? 'mt-8' : 'mt-28'} text-center space-y-4`}>
                    {/* Card Navigation Controls */}
                    {allCards.length > 1 && (
                        <div className={`flex items-center justify-center ${isMobile ? 'space-x-2' : 'space-x-4'} mb-4`}>
                            <motion.button
                                onClick={goToPreviousCard}
                                disabled={currentCardIndex === 0}
                                className={`${isMobile ? 'p-1.5' : 'p-2'} rounded-full ${
                                    currentCardIndex === 0
                                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                        : 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:opacity-90'
                                } transition shadow-md`}
                                whileHover={currentCardIndex !== 0 ? { scale: 1.05 } : {}}
                                whileTap={currentCardIndex !== 0 ? { scale: 0.95 } : {}}
                            >
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                </svg>
                            </motion.button>

                            {/* Card Indicators */}
                            <div className="flex space-x-2">
                                {allCards.map((_, index) => (
                                    <motion.button
                                        key={index}
                                        onClick={() => goToCard(index)}
                                        className={`w-3 h-3 rounded-full transition ${
                                            index === currentCardIndex
                                                ? 'bg-gradient-to-r from-blue-600 to-purple-600'
                                                : 'bg-gray-300 hover:bg-gray-400'
                                        }`}
                                        whileHover={{ scale: 1.2 }}
                                        whileTap={{ scale: 0.9 }}
                                    />
                                ))}
                            </div>

                            <motion.button
                                onClick={goToNextCard}
                                disabled={currentCardIndex === allCards.length - 1}
                                className={`p-2 rounded-full ${
                                    currentCardIndex === allCards.length - 1
                                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                        : 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:opacity-90'
                                } transition shadow-md`}
                                whileHover={currentCardIndex !== allCards.length - 1 ? { scale: 1.05 } : {}}
                                whileTap={currentCardIndex !== allCards.length - 1 ? { scale: 0.95 } : {}}
                            >
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                            </motion.button>
                        </div>
                    )}

                    {/* Card Counter */}
                    {allCards.length > 1 && (
                        <motion.p
                            className="text-sm text-gray-600 font-medium"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.3 }}
                        >
                            Card {currentCardIndex + 1} of {allCards.length}
                        </motion.p>
                    )}

                    <motion.p
                        className="text-sm text-gray-500"
                        animate={{
                            opacity: [0.6, 1, 0.6],
                        }}
                        transition={{
                            duration: 2,
                            repeat: Infinity,
                        }}
                    >
                        Click to flip
                    </motion.p>

                    {/* Keyboard navigation hint */}
                    {allCards.length > 1 && (
                        <motion.p
                            className="text-xs text-gray-400 font-bold"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.5 }}
                        >
                            Tip : Use (←) (→) arrow keys on your keyboard to navigate
                        </motion.p>
                    )}
                    <div className="flex space-x-4 justify-center">
                        <motion.button
                            onClick={() => setIsFlipped(!isFlipped)}
                            className="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:opacity-90 transition shadow-md"
                            whileHover={{ scale: 1.05, boxShadow: "0px 0px 15px rgba(99, 102, 241, 0.5)" }}
                            whileTap={{ scale: 0.95 }}
                        >
                            Flip Card
                        </motion.button>
                    </div>
                </div>
            </div>
        );
    };




    // Mobile action menu component
    const MobileActionMenu = ({ member, isOpen, onClose }) => {
    const currentUserId = localStorage.getItem('user_id');
    
    if (!isOpen) return null;
    
    return createPortal(
        <div 
            className="fixed inset-0 bg-black bg-opacity-60 z-[9999] flex items-center justify-center"  //I dont care anymore just leave it at 9999
            style={{
                position: 'fixed',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                backdropFilter: 'blur(2px)'
            }}
            onClick={onClose}
        >
            <div 
                className="bg-white rounded-lg p-4 m-4 w-full max-w-sm relative shadow-2xl"
                style={{
                    zIndex: 10000,
                    backgroundColor: '#ffffff',
                    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.5)',
                    border: '1px solid rgba(0, 0, 0, 0.1)'
                }}
                onClick={(e) => e.stopPropagation()}
            >
                <div className="flex items-center mb-4 border-b pb-3">
                    <img
                        src={member.image || profile_img}
                        alt="Profile"
                        className="w-10 h-10 rounded-full object-cover mr-3"
                    />
                    <div>
                        <h3 className="font-semibold">{member.name}</h3>
                        <p className="text-sm text-gray-500">{member.position}</p>
                    </div>
                </div>

                <div className="space-y-2 bg-gray-50 p-2 rounded-lg">
                    {/* View Card */}
                    {member.cards && member.cards.length > 0 && (
                        <button
                            className="w-full flex items-center p-3 text-left bg-white hover:bg-blue-50 rounded-lg border border-gray-200"
                            onClick={() => {
                                viewMemberCard(member);
                                onClose();
                            }}
                        >
                            <FaRegEye className="mr-3 text-blue-500" size={18} />
                            <span>View Card</span>
                        </button>
                    )}

                    {/* Edit */}
                    <button
                        className="w-full flex items-center p-3 text-left bg-white hover:bg-green-50 rounded-lg border border-gray-200"
                        onClick={() => {
                            editMember(member);
                            onClose();
                        }}
                    >
                        <FiEdit className="mr-3 text-green-500" size={18} />
                        <span>Edit Member</span>
                    </button>

                    {/* Delete */}
                    {member.id.toString() !== currentUserId && (
                        <button
                            className="w-full flex items-center p-3 text-left bg-white hover:bg-red-50 rounded-lg border border-gray-200 text-red-600"
                            onClick={() => {
                                handleDeleteClick(member);
                                onClose();
                            }}
                        >
                            <TfiTrash className="mr-3" size={18} />
                            <span>Delete Member</span>
                        </button>
                    )}
                </div>

                <button
                    className="w-full mt-4 p-2 bg-gray-200 hover:bg-gray-300 rounded-lg text-center font-medium transition-colors"
                    onClick={onClose}
                >
                    Cancel
                </button>
            </div>
        </div>,
        document.body
    );
};

    // Mobile list view component
    const MobileListView = () => {
        if (loading) {
            return (
                <div className="space-y-2">
                    {[...Array(5)].map((_, index) => (
                        <div key={index} className="bg-white border rounded-lg p-4 shadow-sm animate-pulse">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center flex-1">
                                    <div className="w-12 h-12 bg-gray-300 rounded-full mr-3"></div>
                                    <div className="flex-1">
                                        <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                                        <div className="h-3 bg-gray-300 rounded w-1/2 mb-1"></div>
                                        <div className="h-3 bg-gray-300 rounded w-1/3"></div>
                                    </div>
                                </div>
                                <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                            </div>
                        </div>
                    ))}
                </div>
            );
        }

        if (!data || data.length === 0) {
            return (
                <div className="text-center py-8">
                    <p className="text-gray-500">No members found</p>
                </div>
            );
        }

        return (
            <div className="space-y-2">
                {data.map((member) => (
                    <div key={member.id} className="bg-white border rounded-lg p-4 shadow-sm">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center flex-1">
                                <img
                                    src={member.image || profile_img}
                                    alt="Profile"
                                    className="w-12 h-12 rounded-full object-cover mr-3"
                                />
                                <div className="flex-1">
                                    <h3 className="font-semibold text-gray-900">{member.name}</h3>
                                    <p className="text-sm text-gray-500">{member.position}</p>
                                    <p className="text-xs text-gray-400">{member.department}</p>
                                    {member.cards && member.cards.length > 0 && (
                                        <p className="text-xs text-blue-600 mt-1">
                                            {member.cards.length === 1
                                                ? member.cards[0]?.number
                                                : `${member.cards.length} Cards`
                                            }
                                        </p>
                                    )}
                                </div>
                            </div>
                            <button
                                className="p-2 hover:bg-gray-100 rounded-full"
                                onClick={() => setMobileActionMenuOpen(member.id)}
                            >
                                <HiDotsVertical className="text-gray-500" size={20} />
                            </button>
                        </div>
                    </div>
                ))}

                {/* Mobile Action Menu */}
                {mobileActionMenuOpen && (
                    <MobileActionMenu
                        member={data.find(m => m.id === mobileActionMenuOpen)}
                        isOpen={!!mobileActionMenuOpen}
                        onClose={() => setMobileActionMenuOpen(null)}
                    />
                )}
            </div>
        );
    };

    const Header = () => {
        // Removed unused variables
        return (
            <div className="w-full flex justify-between">
                {/* <TemplateImageHeader /> */}
                <div className="flex items-center space-x-2">

                <button
                    onClick={handleExport}
                    className="main-btn text-md shadow-md mr-[20px] flex items-center"
                >
                    <FaFileExport className="mr-2" />
                    <span>Export Excel</span>
                </button>


                <button
                    onClick={() => setImportDialogVisible(true)}
                    className="main-btn text-md shadow-md mr-[20px] flex items-center"
                >
                    <FaFileImport className="mr-2" />
                    <span>Import Excel</span>
                </button>

                </div>
            </div>
        )
    }


    return (
        <div className="w-full min-w-full h-full flex flex-col">

        <Toast ref={toast} position="top-right" />

        <ConfirmDialog                                       //This is a small confirm modal for the delete button
            group="headless"
            content={(options) => (
                <div className="flex flex-col items-center p-5">
                    <i className="pi pi-exclamation-triangle text-6xl text-yellow-500 mb-3"/>
                    <span className="text-xl font-bold mb-2">{options.message}</span>
                    <div className="flex gap-3">
                        <button className="p-button p-component" onClick={options.accept}>
                            Yes
                        </button>
                        <button className="p-button p-component p-button-outlined" onClick={options.reject}>
                            No
                        </button>
                    </div>
                </div>
            )}
        />



            {/*Buttons & Search Bar Container*/}
            <div className={`w-full mb-4 gap-4 mt-1 ${isMobile ? 'space-y-4' : 'flex justify-between items-start'}`}>
                {/* Action buttons */}
                <div className={`${isMobile ? 'flex flex-wrap gap-2' : 'flex items-center'}`}>
                    <button className={`main-btn ${isMobile ? 'text-sm' : 'text-md'} shadow-md ${isMobile ? 'mr-2' : 'mr-[20px]'}`} onClick={() => createMember()}>
                        Add Member
                    </button>

                   {!isMobile && <button
                        className={`${activeBtn ? "gray-btn" : "main-btn"} ${isMobile ? 'text-sm' : 'text-md'} me-2 shadow-md`}
                        disabled={activeBtn}
                        onClick={() => createGroup()}
                    >
                        Create Group
                    </button>                       //This is so mobile users can't create groups since they cant select
                    }


                    {/* Mobile export/import buttons */}
                    {isMobile && (
                        <>
                            <button
                                onClick={handleExport}
                                className="main-btn text-sm shadow-md flex items-center"
                            >
                                <FaFileExport className="mr-1" size={14} />
                                <span>Export</span>
                            </button>

                            <button
                                onClick={() => setImportDialogVisible(true)}
                                className="main-btn text-sm shadow-md flex items-center"
                            >
                                <FaFileImport className="mr-1" size={14} />
                                <span>Import</span>
                            </button>
                        </>
                    )}
                </div>

                {/* Search input */}
                <div className={`relative ${isMobile ? 'w-full' : 'flex-grow max-w-[700px]'}`}>
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <FaSearch className="h-4 w-4 text-gray-400" aria-hidden="true" />
                    </div>
                    <input
                        type="text"
                        placeholder="Search by name..."
                        className="w-full pl-10 pr-4 py-2 border rounded-md shadow-md
                                focus:outline-none focus:ring-2 focus:ring-blue-300
                                focus:border-blue-300 transition-all duration-200"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                    />
                </div>

                {/* Desktop export/import buttons */}
                {!isMobile && (
                    <div className="flex items-center space-x-2">
                        <button
                            onClick={handleExport}
                            className="main-btn text-md shadow-md mr-[20px] flex items-center"
                        >
                            <FaFileExport className="mr-2" />
                            <span>Export Excel</span>
                        </button>

                        <button
                            onClick={() => setImportDialogVisible(true)}
                            className="main-btn text-md shadow-md mr-[20px] flex items-center"
                        >
                            <FaFileImport className="mr-2" />
                            <span>Import Excel</span>
                        </button>
                    </div>
                )}
            </div>



            <div className="flex-grow h-full">
                {isMobile ? (
                    // Mobile view
                    <div className="h-full overflow-y-auto">
                        <MobileListView />

                        {/* Mobile pagination */}
                        {totalRecords > lazyParams?.rows && (
                            <div className="flex justify-between items-center mt-4 p-4 bg-white border-t">
                                <button
                                    className="px-4 py-2 bg-blue-500 text-white rounded disabled:bg-gray-300"
                                    disabled={lazyParams?.first === 0}
                                    onClick={() => dataHandler({
                                        first: Math.max(0, lazyParams.first - lazyParams.rows),
                                        page: Math.max(0, lazyParams.page - 1)
                                    })}
                                >
                                    Previous
                                </button>
                                <span className="text-sm text-gray-600">
                                    {lazyParams?.first + 1} - {Math.min(lazyParams?.first + lazyParams?.rows, totalRecords)} of {totalRecords}
                                </span>
                                <button
                                    className="px-4 py-2 bg-blue-500 text-white rounded disabled:bg-gray-300"
                                    disabled={lazyParams?.first + lazyParams?.rows >= totalRecords}
                                    onClick={() => dataHandler({
                                        first: lazyParams.first + lazyParams.rows,
                                        page: lazyParams.page + 1
                                    })}
                                >
                                    Next
                                </button>
                            </div>
                        )}
                    </div>
                ) : (
                    // Desktop view
                    <DataTable
                        className="table border w-full h-full"
                        // scrollHeight="flex"
                        selection={selectedMembers.data}
                        onSelectionChange={(e) => selectAllHandler(e.value)}
                        lazy
                        // filterDisplay="row"
                        // header={Header}
                        responsiveLayout="stack"
                        breakpoint="960px"
                        dataKey="id"
                        paginator
                        value={data}
                        first={lazyParams?.first}
                        rows={lazyParams?.rows}
                        rowsPerPageOptions={[5, 25, 50, 100]}
                        totalRecords={totalRecords}
                        onPage={(e) => {
                            // Only update if actually changed
                            if (e.first !== lazyParams.first || e.rows !== lazyParams.rows) {
                                dataHandler({
                                    first: e.first,
                                    rows: e.rows,
                                    page: Math.floor(e.first / e.rows)
                                });
                            }
                        }}
                        onSort={(e) => {
                            // Only update if actually changed
                            if (e.sortField !== lazyParams.sortField || e.sortOrder !== lazyParams.sortOrder) {
                                dataHandler({
                                    sortField: e.sortField,
                                    sortOrder: e.sortOrder
                                });
                            }
                        }}
                        onRowsPerPageChange={(e) => {
                            // Only update if actually changed
                            if (e.rows !== lazyParams.rows) {
                                dataHandler({
                                    rows: e.rows,
                                    first: 0,
                                    page: 0
                                });
                            }
                        }}
                        sortField={lazyParams?.sortField}
                        sortOrder={lazyParams?.sortOrder}
                        onFilter={(e) => {
                            // Only update if filters actually changed
                            dataHandler({
                                filters: e.filters
                            });
                        }}
                        filters={lazyParams?.filters}
                        loading={loading}
                        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"
                        scrollable
                        scrollHeight="100%"
                    >
                        <Column selectionMode="multiple" headerStyle={{ width: '3rem' }} exportable={false} />
                        {/* <Column body={profileBodyTemplate} header="Profile Image" className="text-center" /> */}
                        {/* <Column body={imageBodyTemplate} header="Template Image" className="text-center" /> */}
                        <Column
                            field="name"
                            header="Name"
                            className="text-left"
                            sortable
                            body={(rowData) => (
                                <div className="flex items-center gap-2">
                                    <img
                                        src={rowData.image || profile_img} // Adjust this key to match your data
                                        // alt="Profile"
                                        className="w-8 h-8 rounded-full object-cover"
                                    />
                                    <span>{rowData.name}</span>
                                </div>
                            )}
                        />
                        <Column field="type" header="Type" className="text-center" filter sortable showFilterMenu={false} />
                        <Column field="position" header="Position" className="text-center" filter sortable showFilterMenu={false} />
                        <Column
                            field="cards"
                            header="Associated Card(s)"
                            body={(rowData) => {
                                if (!rowData.cards || rowData.cards.length === 0) {
                                    return 'No Card';
                                } else if (rowData.cards.length === 1) {
                                    return rowData.cards[0]?.number;
                                } else {
                                    return (
                                        <span>
                                            <span className="text-blue-600 font-semibold">{rowData.cards.length}</span> Cards
                                        </span>
                                    );
                                }
                            }}
                        />
                        <Column field="department" header="Department" className="text-center" filter sortable showFilterMenu={false} />
                        <Column body={actionBodyTemplate} header="Action" className="text-center w-96" exportable={false} style={{ minWidth: '8rem' }} />

                        {userType === 'admin' && (
                            <Column field="status" body={statusBodyTemplate} header="Package Status" className="text-center" showFilterMenu={false} filter sortable />
                        )}
                    </DataTable>
                )}
            </div>

            {/* Dialogs */}

            <ImportDialog
                visible={importDialogVisible}
                onHide={() => setImportDialogVisible(false)}
                onImportSuccess={handleImportSuccess}
            />
            {openDialog?.addMember && <AddMemberDialog data={selectedMember} actionType={actionType} />}
            {openDialog?.updateGroup && <AssignGroupDialog />}

            {/* Create Group Modal */}
            {isCreateGroupModalOpen && (
                <GroupForm
                    isModalOpen={isCreateGroupModalOpen}
                    setIsModalOpen={setIsCreateGroupModalOpen}
                    onSuccess={handleGroupSuccess}
                    toast={toast}
                    preselectedMembers={selectedMembers.data}
                />
            )}

            {/* Card View Dialog */}
            <Dialog
                header={allCards.length > 1 ? `Card Preview (${allCards.length} cards)` : "Card Preview"}
                visible={isCardViewOpen}
                style={{
                    width: isMobile ? '95vw' : '50vw',
                    maxWidth: isMobile ? '95vw' : '800px',
                    height: isMobile ? '90vh' : 'auto'
                }}
                breakpoints={{
                    '960px': '95vw',
                    '641px': '95vw'
                }}
                onHide={() => {
                    setIsCardViewOpen(false);
                    setIsFlipped(false);
                    setCurrentCardIndex(0);
                    setAllCards([]);
                }}
                className={`badge-view-dialog ${isMobile ? 'mobile-card-dialog' : ''}`}
                contentStyle={{
                    height: isMobile ? 'calc(90vh - 60px)' : 'auto',
                    overflow: isMobile ? 'auto' : 'visible',
                    padding: isMobile ? '10px' : '20px'
                }}
            >
                {render3DCard()}
            </Dialog>
        </div>
    );
}

export default MemberDataTable;