// Template data for the DesignSpace
// Each template has a unique ID, name, dimensions, thumbnail URL, and elements array

// Business Card Templates
export const businessCardTemplates = [
  {
    id: 'bc-001',
    name: 'Modern Business Card',
    width: 350,
    height: 200,
    thumbnail: 'https://placehold.co/350x200/4338ca/ffffff?text=Modern+Business+Card',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 350,
        height: 200,
        backgroundColor: '#4338ca',
      },
      {
        id: 'el_2',
        type: 'text',
        x: 30,
        y: 50,
        width: 290,
        height: 40,
        value: 'JOHN DOE',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'left',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 30,
        y: 90,
        width: 290,
        height: 20,
        value: 'Software Engineer',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#e0e0e0',
        textAlign: 'left',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 30,
        y: 130,
        width: 290,
        height: 20,
        value: '<EMAIL> | +1 (555) 123-4567',
        fontSize: 12,
        fontWeight: 'normal',
        color: '#e0e0e0',
        textAlign: 'left',
      },
    ]
  },
  {
    id: 'bc-002',
    name: 'Elegant Business Card',
    width: 350,
    height: 200,
    thumbnail: 'https://placehold.co/350x200/ffffff/333333?text=Elegant+Business+Card',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 350,
        height: 200,
        backgroundColor: '#ffffff',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 10,
        height: 200,
        backgroundColor: '#4338ca',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 30,
        y: 50,
        width: 290,
        height: 40,
        value: 'JANE SMITH',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#333333',
        textAlign: 'left',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 30,
        y: 90,
        width: 290,
        height: 20,
        value: 'Marketing Director',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'left',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 30,
        y: 130,
        width: 290,
        height: 20,
        value: '<EMAIL> | +1 (555) 987-6543',
        fontSize: 12,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'left',
      },
    ]
  },
  {
    id: 'bc-003',
    name: 'Minimalist Business Card',
    width: 350,
    height: 200,
    thumbnail: 'https://placehold.co/350x200/f5f5f5/333333?text=Minimalist+Business+Card',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 350,
        height: 200,
        backgroundColor: '#f5f5f5',
      },
      {
        id: 'el_2',
        type: 'text',
        x: 30,
        y: 70,
        width: 290,
        height: 40,
        value: 'ALEX JOHNSON',
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333333',
        textAlign: 'center',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 30,
        y: 110,
        width: 290,
        height: 20,
        value: 'UX Designer',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'shape',
        shapeType: 'rectangle',
        x: 125,
        y: 140,
        width: 100,
        height: 1,
        backgroundColor: '#333333',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 30,
        y: 150,
        width: 290,
        height: 20,
        value: '<EMAIL>',
        fontSize: 12,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'center',
      },
    ]
  },
];

// ID Badge Templates
export const idBadgeTemplates = [
  {
    id: 'id-001',
    name: 'Corporate ID Badge',
    width: 300,
    height: 450,
    thumbnail: 'https://placehold.co/300x450/ffffff/333333?text=Corporate+ID+Badge',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 300,
        height: 450,
        backgroundColor: '#ffffff',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 300,
        height: 80,
        backgroundColor: '#4338ca',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 20,
        y: 25,
        width: 260,
        height: 30,
        value: 'ACME CORPORATION',
        fontSize: 18,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'shape',
        shapeType: 'rectangle',
        x: 75,
        y: 100,
        width: 150,
        height: 150,
        backgroundColor: '#f0f0f0',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 20,
        y: 270,
        width: 260,
        height: 40,
        value: 'JOHN DOE',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#333333',
        textAlign: 'center',
      },
      {
        id: 'el_6',
        type: 'text',
        x: 20,
        y: 310,
        width: 260,
        height: 30,
        value: 'Software Engineer',
        fontSize: 16,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'center',
      },
      {
        id: 'el_7',
        type: 'text',
        x: 20,
        y: 350,
        width: 260,
        height: 20,
        value: 'ID: EMP12345',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'center',
      },
      {
        id: 'el_8',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 400,
        width: 300,
        height: 50,
        backgroundColor: '#4338ca',
      },
    ]
  },
  {
    id: 'id-002',
    name: 'Event Badge',
    width: 300,
    height: 450,
    thumbnail: 'https://placehold.co/300x450/f5f5f5/333333?text=Event+Badge',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 300,
        height: 450,
        backgroundColor: '#f5f5f5',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 300,
        height: 100,
        backgroundColor: '#ff5722',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 20,
        y: 35,
        width: 260,
        height: 30,
        value: 'TECH CONFERENCE 2023',
        fontSize: 16,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 20,
        y: 150,
        width: 260,
        height: 60,
        value: 'JOHN DOE',
        fontSize: 36,
        fontWeight: 'bold',
        color: '#333333',
        textAlign: 'center',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 20,
        y: 220,
        width: 260,
        height: 30,
        value: 'ATTENDEE',
        fontSize: 20,
        fontWeight: 'normal',
        color: '#ff5722',
        textAlign: 'center',
      },
      {
        id: 'el_6',
        type: 'shape',
        shapeType: 'rectangle',
        x: 50,
        y: 270,
        width: 200,
        height: 1,
        backgroundColor: '#cccccc',
      },
      {
        id: 'el_7',
        type: 'text',
        x: 20,
        y: 290,
        width: 260,
        height: 20,
        value: 'Company: ACME Corporation',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'center',
      },
      {
        id: 'el_8',
        type: 'text',
        x: 20,
        y: 320,
        width: 260,
        height: 20,
        value: 'June 15-17, 2023',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'center',
      },
    ]
  },
];

// Social Media Templates
export const socialMediaTemplates = [
  {
    id: 'sm-001',
    name: 'Instagram Post',
    width: 1080,
    height: 1080,
    thumbnail: 'https://placehold.co/1080x1080/4338ca/ffffff?text=Instagram+Post',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1080,
        height: 1080,
        backgroundColor: '#4338ca',
      },
      {
        id: 'el_2',
        type: 'text',
        x: 100,
        y: 400,
        width: 880,
        height: 200,
        value: 'YOUR AMAZING CONTENT HERE',
        fontSize: 60,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 100,
        y: 600,
        width: 880,
        height: 100,
        value: 'Follow us for more inspiration',
        fontSize: 30,
        fontWeight: 'normal',
        color: '#e0e0e0',
        textAlign: 'center',
      },
    ]
  },
];

// Presentation Templates
export const presentationTemplates = [
  {
    id: 'pr-001',
    name: 'Business Presentation',
    width: 1920,
    height: 1080,
    thumbnail: 'https://placehold.co/1920x1080/ffffff/333333?text=Business+Presentation',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1920,
        height: 1080,
        backgroundColor: '#ffffff',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1920,
        height: 120,
        backgroundColor: '#4338ca',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 100,
        y: 40,
        width: 1720,
        height: 40,
        value: 'PRESENTATION TITLE',
        fontSize: 36,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'left',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 100,
        y: 200,
        width: 1720,
        height: 80,
        value: 'Slide Title Goes Here',
        fontSize: 60,
        fontWeight: 'bold',
        color: '#333333',
        textAlign: 'left',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 100,
        y: 350,
        width: 800,
        height: 400,
        value: '• First bullet point\n• Second bullet point\n• Third bullet point\n• Fourth bullet point',
        fontSize: 30,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'left',
      },
    ]
  },
];

// Print Templates
export const printTemplates = [
  {
    id: 'pr-001',
    name: 'Flyer',
    width: 800,
    height: 1200,
    thumbnail: 'https://placehold.co/800x1200/f5f5f5/333333?text=Flyer',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 800,
        height: 1200,
        backgroundColor: '#f5f5f5',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 800,
        height: 200,
        backgroundColor: '#4338ca',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 50,
        y: 70,
        width: 700,
        height: 60,
        value: 'EVENT TITLE',
        fontSize: 48,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'shape',
        shapeType: 'rectangle',
        x: 100,
        y: 250,
        width: 600,
        height: 400,
        backgroundColor: '#e0e0e0',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 50,
        y: 700,
        width: 700,
        height: 100,
        value: 'Event Description Goes Here',
        fontSize: 36,
        fontWeight: 'bold',
        color: '#333333',
        textAlign: 'center',
      },
      {
        id: 'el_6',
        type: 'text',
        x: 50,
        y: 800,
        width: 700,
        height: 200,
        value: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
        fontSize: 24,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'center',
      },
      {
        id: 'el_7',
        type: 'text',
        x: 50,
        y: 1050,
        width: 700,
        height: 100,
        value: 'Date: June 15, 2023\nLocation: 123 Main St, Anytown',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#4338ca',
        textAlign: 'center',
      },
    ]
  },
];

// Import additional templates
import { professionalBusinessCards, professionalIdBadges, additionalTemplates } from './TemplatesData.additional';
import { creativeBusinessCards, moreTemplates } from './TemplatesData.more';
import { corporateIdBadges, eventBadges, allBadgeTemplates } from './TemplatesData.badges';
import { instagramTemplates, facebookTemplates, allSocialTemplates } from './TemplatesData.social';
import { instagramStoryTemplates, twitterPostTemplates, linkedinPostTemplates, moreSocialTemplates } from './TemplatesData.social.more';
import { businessPresentationTemplates, allPresentationTemplates } from './TemplatesData.presentation';
import { flyerTemplates, brochureTemplates, allPrintTemplates } from './TemplatesData.print';
import { photoLayoutTemplates, photoFrameTemplates, allPhotoTemplates } from './TemplatesData.photos';
import { videoThumbnailTemplates, videoIntroTemplates, allVideoTemplates } from './TemplatesData.videos';
import { modernBusinessCards, luxuryBusinessCards, allBusinessCardTemplates } from './TemplatesData.business';

// Combine all templates
export const allTemplates = [
  ...businessCardTemplates,
  ...idBadgeTemplates,
  ...socialMediaTemplates,
  ...presentationTemplates,
  ...printTemplates,
  ...additionalTemplates,
  ...moreTemplates,
  ...allBadgeTemplates,
  ...allSocialTemplates,
  ...allPresentationTemplates,
  ...allPrintTemplates,
  ...allPhotoTemplates,
  ...allVideoTemplates,
  ...allBusinessCardTemplates,
  ...moreSocialTemplates,
];
