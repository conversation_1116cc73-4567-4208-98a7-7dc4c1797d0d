import { useEffect, useRef, useState } from 'react';
import { isEmpty } from 'lodash';

import { useUploadMutation } from '@quires';
import { useDesignSpace } from "@contexts/DesignSpaceContext";

import { RiImage2Line, RiImageAddLine } from 'react-icons/ri';
import { ProgressSpinner } from 'primereact/progressspinner';
import { FileUpload } from 'primereact/fileupload';
import { Slider } from "primereact/slider";
import { Dialog } from 'primereact/dialog';
import LoadOnScroll from './LoadOnScroll';
import ImageEditor from '../components/ImageEditor';

function ImageSettings() {
  const { addElement, selectedElement, setSelectedElement, setElements, elements } = useDesignSpace();
  const [refetch, setRefetch] = useState(true);
  const [showImageEditor, setShowImageEditor] = useState(false);

  const uploadImage = useUploadMutation()
  const fileUploadRef = useRef(null);

  const imageSettingHandler = (key, value) => {
    if (!selectedElement) return;

    setSelectedElement(prev => ({ ...prev, [key]: value }))
    const updatedElements = elements.map((el) => el.id === selectedElement.id ? { ...el, [key]: value } : el);
    setElements(updatedElements);
  }

  const imageHandler = async (file) => {
    if (file) {
      const formData = new FormData();
      formData.append("file", file)
      formData.append("file_type", "image")
      formData.append("user_id", localStorage.getItem("user_id"));

      await uploadImage.mutateAsync(formData,
        {
          onSuccess: (data) => {
            // addElement("img", data?.file_url)
            fileUploadRef.current.clear()
            setRefetch(true)
          }
        }
      )
    }
  };

  const openImageEditor = () => {
    if (selectedElement && selectedElement.type === 'img') {
      setShowImageEditor(true);
    }
  };

  const ButtonIcon = () => {
    return (
      uploadImage.isLoading ?
        <ProgressSpinner style={{ width: '30px', height: '30px' }}
          strokeWidth="1"
          fill="transparent"
          animationDuration="2s"
        />
        :
        <RiImageAddLine size={24} className="mb-2" />
    )
  }

  return (
    <>
      <div className="flex justify-start flex-wrap">
        <div className="w-full flex">
          <button className="w-4/12 me-2 my-2 add-element-btn" onClick={() => addElement("img")}>
            <RiImage2Line size={24} className="mb-2" />
            <span>Add Image</span>
          </button>

          <FileUpload
            mode="basic"
            name="image"
            accept="image/*"
            maxFileSize={500 * 1024}
            customUpload
            onSelect={(e) => imageHandler(e.files[0])}
            ref={fileUploadRef}
            onClick={() => fileUploadRef.current.clear()}
            className='w-4/12 me-2 my-2 add-element-btn'
            disabled={uploadImage.isLoading}
            chooseOptions={{
              icon: <ButtonIcon />,
              label: 'Upload Image',
              style: {
                display: "flex",
                flexDirection: "column",
                background: 'transparent',
                color: '#676666',
                width: "100%",
                border: "none",
                fontWeight: "normal",
                fontSize: "16px",
                padding: 0,
              }
            }}
          />

          {/* <button
            className={`w-4/12 me-2 my-2 add-element-btn ${!selectedElement || selectedElement.type !== 'img' ? 'opacity-50 cursor-not-allowed' : ''}`}
            onClick={openImageEditor}
            disabled={!selectedElement || selectedElement.type !== 'img'}
          >
            <RiImage2Line size={24} className="mb-2" />
            <span>Advanced Edit</span>
          </button> */}
        </div>

        <div className="w-full mt-5 m-2">
          <h4 className="font-bold mb-2 text-[#676666]">Edit Image</h4>

          <label className='mb-4 text-sm' htmlFor="borderRadius"> Border Radius: {selectedElement?.borderRadius}% </label>
          <Slider
            id="borderRadius"
            value={selectedElement?.borderRadius}
            onChange={(e) => imageSettingHandler("borderRadius", e.value)} // Update border-radius state
            min={0}
            max={100} // Range for border-radius (0% to 50%)
            className="my-3 mx-2"
            disabled={isEmpty(selectedElement)}
          />
        </div>

        <div className="flex flex-col justify-start m-2">
          <h4 className="font-bold mt-4 mb-2 text-[#676666]">Library</h4>
          <LoadOnScroll fileType="image" refetch={refetch} setRefetch={setRefetch} />
        </div>
      </div>

      {/* Image Editor Dialog */}
      <Dialog
        visible={showImageEditor}
        onHide={() => setShowImageEditor(false)}
        header="Advanced Image Editor"
        style={{ width: '90%', maxWidth: '800px' }}
        modal
        className="p-fluid"
      >
        {selectedElement && selectedElement.type === 'img' && (
          <ImageEditor
            imageId={selectedElement.id}
            onClose={() => setShowImageEditor(false)}
          />
        )}
      </Dialog>
    </>
  )
}

export default ImageSettings